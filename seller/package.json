{"name": "lilishop", "version": "1.0.0", "private": true, "description": "lilishop-ui", "author": "lili-platform", "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "dev": "vue-cli-service serve"}, "dependencies": {"@amap/amap-jsapi-loader": "0.0.7", "@antv/g2": "^4.1.14", "axios": "^0.21.1", "dplayer": "^1.27.1", "js-cookie": "^2.2.1", "node-sass": "^4.14.1", "price-color": "^1.0.2", "s": "^1.0.0", "sass-loader": "^8.0.2", "sockjs-client": "^1.4.0", "swiper": "^6.3.5", "uuid": "^8.3.2", "view-design": "^4.6.1", "vue": "^2.6.10", "vue-awesome-swiper": "^4.1.1", "vue-json-excel": "^0.3.0", "vue-lazyload": "^1.3.3", "vue-print-nb": "^1.7.5", "vue-qr": "^2.3.0", "vue-router": "^3.1.3", "vuedraggable": "^2.23.2", "vuex": "^3.4.0", "xss": "^1.0.7"}, "devDependencies": {"@vue/cli-plugin-babel": "^4.4.4", "@vue/cli-plugin-router": "^4.4.4", "@vue/cli-plugin-vuex": "^4.4.4", "@vue/cli-service": "^4.4.4", "compression-webpack-plugin": "^4.0.0", "css-loader": "^5.0.1", "less": "^3.12.2", "less-loader": "^6.2.0", "style-loader": "^2.0.0", "style-resources-loader": "^1.3.2", "uglifyjs-webpack-plugin": "^2.2.0", "view-design": "^4.6.1", "vue-cli-plugin-style-resources-loader": "^0.1.4", "vue-template-compiler": "^2.6.10"}}