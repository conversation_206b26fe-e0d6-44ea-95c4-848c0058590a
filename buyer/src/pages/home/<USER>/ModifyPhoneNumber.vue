<template>
  <div class="wrapper">
    <card _Title="修改手机号" />
    <div class="form-container">
      <!-- 步骤1: 验证旧手机 -->
      <Form ref="oldPhoneForm" :model="oldPhoneFormData" :rules="oldPhoneFormRules" :label-width="100" v-if="currentStep === 1">
        <FormItem label="当前手机号">
          <span>{{ desensitizedCurrentMobile || '加载中...' }}</span>
        </FormItem>
        <FormItem label="短信验证码" prop="oldMobileCode" class="code-form-item">
          <div class="code-row">
            <i-input v-model="oldPhoneFormData.oldMobileCode" placeholder="请输入旧手机验证码" style="width: 200px;"></i-input>
            <Button @click="handleSendOldPhoneCode" :disabled="isCountingDownOld">
              {{ countDownButtonTextOld }}
            </Button>
          </div>
        </FormItem>
        <FormItem>
          <Button type="primary" @click="validateOldPhoneStep">下一步</Button>
        </FormItem>
      </Form>

      <!-- 步骤2: 绑定新手机 -->
      <Form
        ref="newPhoneForm"
        :model="newPhoneFormData"
        :rules="newPhoneFormRules"
        :label-width="100"
        v-if="currentStep === 2"
        :key="'newPhoneFormKey'"
      >
        <FormItem label="新手机号" prop="newMobile">
          <i-input v-model="newPhoneFormData.newMobile" placeholder="请输入新手机号"></i-input>
        </FormItem>
        <FormItem label="短信验证码" prop="newMobileCode">
          <i-input v-model="newPhoneFormData.newMobileCode" placeholder="请输入新手机验证码" style="width: 200px;"></i-input>
          <Button @click="handleSendNewPhoneCode" :disabled="isCountingDownNew">
            {{ countDownButtonTextNew }}
          </Button>
        </FormItem>
        <FormItem>
          <Button type="primary" @click="handleSubmitChange">确认修改</Button>
        </FormItem>
      </Form>
    </div>
    <!-- Verify component wrapped for controlled width and centering -->
    <div class="verify-component-wrapper">
      <verify ref="sliderVerify" verifyType="VERIFY_OLD_PHONE" @change="handleSliderVerificationChange"></verify>
    </div>
  </div>
</template>

<script>
import card from '@/components/card'; // 确保路径正确
import { validateSmsCode, updatePhoneNumber } from '@/api/account';
import { getMemberMsg } from '@/api/login';
import { sendSms } from '@/api/common'; // getVerifyImg, postVerifyImg will be removed
import storage from '@/plugins/storage';
import * as RegExp from '@/plugins/RegExp'; // 假设有手机号正则
import verify from '@/components/verify/index.vue'; // <-- Import verify component

// VERIFICATION_ENUMS is now only used within the script, not directly in the template for verifyType
const VERIFICATION_ENUMS = {
  VERIFY_OLD_PHONE: 'VERIFY_OLD_PHONE',
  BIND_NEW_PHONE: 'BIND_NEW_PHONE',
  // MODIFY_PHONE_NUMBER_CAPTCHA: 'MODIFY_PHONE_NUMBER' // Commenting out custom enum
  // CAPTCHA_GENERAL: 'LOGIN' // No longer using LOGIN for this context
};

export default {
  name: 'ModifyPhoneNumber',
  components: {
    card,
    verify // <-- Register verify component
  },
  data() {
    const validateMobile = (rule, value, callback) => {
      if (!value) {
        return callback(new Error('手机号不能为空'));
      }
      if (!RegExp.mobile.test(value)) { // 确保 RegExp.mobile 可用
        return callback(new Error('请输入正确的手机号'));
      }
      callback();
    };

    return {
      currentStep: 1, // 1: 验证旧手机, 2: 绑定新手机
      currentUser: null,
      desensitizedCurrentMobile: '',

      oldPhoneFormData: {
        // captcha: '', // Removed captcha field
        oldMobileCode: ''
      },
      newPhoneFormData: {
        newMobile: '',
        newMobileCode: ''
      },

      oldPhoneFormRules: {
        // captcha: [{ required: true, message: '请输入图片验证码', trigger: 'blur' }], // Removed rule for captcha
        oldMobileCode: [{ required: true, message: '请输入旧手机验证码', trigger: 'blur' }]
      },
      newPhoneFormRules: {
        newMobile: [
          { required: true, message: '请输入新手机号', trigger: 'blur' },
          { validator: validateMobile, trigger: 'blur' }
        ],
        newMobileCode: [{ required: true, message: '请输入新手机验证码', trigger: 'blur' }]
      },

      // 旧手机验证码倒计时
      countDownButtonTextOld: '发送验证码',
      isCountingDownOld: false,
      timerOld: null,
      countDownSecondsOld: 180,

      // 新手机验证码倒计时
      countDownButtonTextNew: '发送验证码',
      isCountingDownNew: false,
      timerNew: null,
      countDownSecondsNew: 180,

      // captchaImage: '', // Removed
      // captchaId: '' // Removed
      oldPhoneSliderVerified: false, // <-- Initialize the missing property here
      isAttemptingToSendOldCode: false // <-- New property
    };
  },
  async created() {
    await this.fetchCurrentUserInfo();
  },
  beforeDestroy() {
    if (this.timerOld) clearInterval(this.timerOld);
    if (this.timerNew) clearInterval(this.timerNew);
  },
  methods: {
    async fetchCurrentUserInfo() {
      try {
        const res = await getMemberMsg();
        if (res.success && res.result) {
          this.currentUser = res.result;
          if (this.currentUser.mobile) {
            this.desensitizedCurrentMobile = this.currentUser.mobile.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
          }
        } else {
          this.$Message.error(res.message || '获取用户信息失败，请稍后再试');
        }
      } catch (error) {
        this.$Message.error('获取用户信息请求失败');
        console.error('Error fetching user info:', error);
      }
    },
    async refreshCaptcha() {
      try {
        const res = await getVerifyImg(VERIFICATION_ENUMS.CAPTCHA_GENERAL);
        if (res.success && res.result && res.result.captchaImage) {
          this.captchaImage = res.result.captchaImage;
          this.captchaId = res.result.captchaId;
          this.oldPhoneFormData.captcha = '';
        } else {
          this.$Message.error(res.message || '获取图片验证码失败');
          this.captchaImage = '';
          this.captchaId = '';
        }
      } catch (error) {
        this.$Message.error('获取图片验证码请求失败');
        this.captchaImage = '';
        this.captchaId = '';
        console.error('Error refreshing captcha:', error);
      }
    },

    // 发送验证码通用逻辑 (sendSmsParams for captcha related params)
    async sendVerificationCode(mobile, type, timerProp, buttonTextProp, countingDownProp, secondsProp, verificationEnumsValue) {
      if (!mobile) {
        this.$Message.warning((type === 'old' ? '当前' : '新') + '手机号无效');
        return;
      }

      this[countingDownProp] = true;
      this[buttonTextProp] = this[secondsProp] + 's后重发';

      this[timerProp] = setInterval(() => {
        this[secondsProp]--;
        if (this[secondsProp] < 0) {
          clearInterval(this[timerProp]);
          this[buttonTextProp] = '发送验证码';
          this[countingDownProp] = false;
          this[secondsProp] = 180;
        } else {
          this[buttonTextProp] = this[secondsProp] + 's后重发';
        }
      }, 1000);

      try {
        const params = {
          mobile: mobile,
          verificationEnums: verificationEnumsValue
        };
        const res = await sendSms(params);
        if (res.success) {
          this.$Message.success('验证码发送成功');
        } else {
          this.$Message.error(res.message || '验证码发送失败');
          clearInterval(this[timerProp]);
          this[buttonTextProp] = '发送验证码';
          this[countingDownProp] = false;
          this[secondsProp] = 180;
        }
      } catch (error) {
        this.$Message.error('发送验证码请求失败');
        clearInterval(this[timerProp]);
        this[buttonTextProp] = '发送验证码';
        this[countingDownProp] = false;
        this[secondsProp] = 180;
        console.error('Error sending ' + type + ' phone code:', error);
      }
    },

    handleSendOldPhoneCode() {
      if (!this.currentUser || !this.currentUser.mobile) {
        this.$Message.error('无法获取当前手机号');
        return;
      }
      // Removed: if (!this.oldPhoneSliderVerified) { ... }

      this.isAttemptingToSendOldCode = true; // Set attempt flag
      this.triggerOldPhoneSliderVerification(); // Always trigger slider
      // Do not call sendVerificationCode directly anymore
    },

    triggerOldPhoneSliderVerification() {
      if (this.$refs.sliderVerify) {
        this.$refs.sliderVerify.init();
      } else {
        this.$Message.error('安全验证组件加载失败');
        this.isAttemptingToSendOldCode = false; // Reset flag if component fails to load
      }
    },

    handleSliderVerificationChange(result) {
      if (result && result.status === true) { // Slider success
        if (this.$refs.sliderVerify) this.$refs.sliderVerify.show = false;
        this.oldPhoneSliderVerified = true; // Mark as verified
        this.$Message.success('安全验证通过');

        if (this.isAttemptingToSendOldCode) {
          // Call sendVerificationCode for old phone
          this.sendVerificationCode(
            this.currentUser.mobile,
            'old',
            'timerOld',
            'countDownButtonTextOld',
            'isCountingDownOld',
            'countDownSecondsOld',
            VERIFICATION_ENUMS.VERIFY_OLD_PHONE
          );
          this.isAttemptingToSendOldCode = false; // Reset attempt flag
          this.oldPhoneSliderVerified = false;    // Reset verification status for next time
        }
      } else { // Slider failed or non-success event
        this.oldPhoneSliderVerified = false;
        this.isAttemptingToSendOldCode = false; // Reset attempt flag
        this.$Message.error('安全验证失败，请重试');
      }
    },

    async validateOldPhoneStep() {
      this.$refs.oldPhoneForm.validate(async (valid) => {
        if (valid) {
          if (!this.currentUser || !this.currentUser.mobile || typeof this.currentUser.mobile !== 'string' || this.currentUser.mobile.trim() === '') {
            this.$Message.error('无法获取有效的当前手机号信息，请刷新页面或稍后再试。');
            return;
          }

          const scene = VERIFICATION_ENUMS.VERIFY_OLD_PHONE;
          const mobile = this.currentUser.mobile;
          const code = this.oldPhoneFormData.oldMobileCode;

          try {
            this.$Spin.show();
            const res = await validateSmsCode(scene, mobile, code);
            this.$Spin.hide();
            if (res.success) {
              this.$Message.success('旧手机验证成功');
              this.currentStep = 2;
            } else {
              this.$Message.error(res.message || '旧手机验证码错误');
            }
          } catch (error) {
            this.$Spin.hide();
            let userMessage = '验证旧手机验证码请求失败';
            if (error && typeof error === 'object') {
              if (error.status === 404) {
                userMessage = '验证服务接口未找到 (404)，请联系技术支持。';
              } else if (error.message) {
                userMessage = error.message;
              } else if (error.error) {
                userMessage = `验证出错: ${error.error} (状态: ${error.status || '未知'})`;
              }
            }
            this.$Message.error(userMessage);
            console.error('Error validating old phone code:', error);
          }
        }
      });
    },

    handleSendNewPhoneCode() {
      this.$nextTick(() => {
        this.$refs.newPhoneForm.validateField('newMobile', async (errMsg) => {
          if (!errMsg) {
            this.sendVerificationCode(
              this.newPhoneFormData.newMobile,
              'new',
              'timerNew',
              'countDownButtonTextNew',
              'isCountingDownNew',
              'countDownSecondsNew',
              VERIFICATION_ENUMS.BIND_NEW_PHONE
            );
          }
        });
      });
    },

    handleSubmitChange() {
      this.$refs.newPhoneForm.validate(async (valid) => {
        if (valid) {
          try {
            this.$Spin.show();
            const params = {
              newMobile: this.newPhoneFormData.newMobile,
              code: this.newPhoneFormData.newMobileCode,
            };
            const res = await updatePhoneNumber(params);
            this.$Spin.hide();
            if (res.success) {
              this.$Message.success('手机号修改成功');
              const updatedUserInfo = await getMemberMsg();
              if (updatedUserInfo.success && updatedUserInfo.result) {
                storage.setItem('userInfo', updatedUserInfo.result);
              }
              this.$router.push({ name: 'AccountSafe' });
            } else {
              this.$Message.error(res.message || '手机号修改失败');
            }
          } catch (error) {
            this.$Spin.hide();
            this.$Message.error('修改手机号请求失败');
            console.error('Error updating phone number:', error);
          }
        }
      });
    }
  }
};
</script>

<style scoped lang="scss">
.wrapper {
  // padding: 20px;
}
.form-container {
  max-width: 500px;
  margin: 20px auto;
  padding: 20px;
  border: 1px solid #dcdee2;
  border-radius: 4px;
}
.verify-component-wrapper {
  max-width: 340px; /* Adjust width as needed, verify component itself is around 320px + padding */
  margin: 20px auto; /* Centering and adding some space */
}
.captcha-img {
  width: 100px;
  height: 32px; /* 与i-input高度对齐 */
  vertical-align: middle;
  margin-left: 10px;
  cursor: pointer;
  border: 1px solid #dcdee2;
}
.captcha-placeholder {
  cursor: pointer;
  color: #909399;
}
.code-row {
  display: flex;
  align-items: center;
  gap: 8px;
}
.code-form-item .ivu-form-item-error-tip,
.code-form-item .ivu-form-item-extra {
  margin-left: 0 !important;
  padding-left: 0 !important;
}
/* 根据需要添加更多样式，参考 ModifyPwd.vue */
</style>
