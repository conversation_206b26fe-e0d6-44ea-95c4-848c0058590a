<template>
  <div style="background: #fff">
    <BaseHeader></BaseHeader>
    <Search></Search>

    <!-- Breadcrumbs and Store Info (Above the columns) -->
    <div class="shop-item-path" v-if="goodsMsg.data">
      <div class="shop-nav-container">
        <Breadcrumb>
          <BreadcrumbItem to="/">首页</BreadcrumbItem>
          <BreadcrumbItem v-for="(item, index) in categoryBar" :to="goGoodsList(index)" target="_blank" :key="index">
            {{ item.name }}
          </BreadcrumbItem>
        </Breadcrumb>
        <div class="store-collect" v-if="!takeDownSale">
          <span class="mr_10" v-if="goodsMsg.data">
            <router-link :to="'Merchant?id=' + goodsMsg.data.storeId">{{
              goodsMsg.data.storeName
            }}</router-link>
          </span>
          <span @click="collect">
            <Icon type="ios-heart" :color="storeCollected ? '#ed3f14' : '#666'" />
            {{ storeCollected? "已收藏店铺": "收藏店铺" }}
          </span>
          <span class="ml_10" @click="IMService(goodsMsg.data.storeId,goodsMsg.data.goodsId,goodsMsg.data.id)">联系客服</span>
        </div>
      </div>
    </div>

    <!-- Moved Store Header Info Wrapper -->
    <div class="store-header-wrapper" v-if="goodsMsg.data">
      <StoreHeaderInfo
        :storeId="goodsMsg.data.storeId"
        :storeName="storeMsg.storeName"
        :storeLogo="storeMsg.storeLogo"
        :goodsId="goodsMsg.data.goodsId"
        :skuId="goodsMsg.data.id"
      />
    </div>

    <!-- Main Content: Left/Right Columns -->
    <div class="goods-detail-main-container" v-if="goodsMsg.data">
      <!-- Left Column -->
      <div class="left-scroll-container">
        <GoodsImages
          :imageList="extractImageList(goodsMsg.data)"
          :videoUrl="goodsMsg.data.goodsVideo"
          :thumbnailUrl="goodsMsg.data.thumbnail"
        />
      </div>
      <!-- Right Sticky Column -->
      <div class="right-sticky-container">
        <GoodsInfo
          :skuInfo="goodsMsg.data"
          :specList="formattedSpecList"
          :currentSelection="currentSelection"
          :promotions="promotions"
          :wholesaleList="goodsMsg.wholesaleList"
          :isPointGoods="$route.query.way === 'POINT'"
          :isCollected="isProductCollected"
          :cartLoading="addingToCart"
          :buyLoading="buyingNow"
          :actionLoading="pointPaying"
          @selectSku="handleSkuSelect"
          @changeCount="handleCountChange"
          @addToCart="handleAddToCart"
          @buyNow="handleBuyNow"
          @pointPay="handlePointPay"
          @collect="handleCollectProduct"
          @couponReceived="handleCouponReceived"
        />
      </div>
    </div>

    <!-- Bottom Content: Full Width Detail Section -->
    <div class="goods-detail-bottom-container" v-if="goodsMsg.data">
      <ShowGoodsDetail :detail="goodsMsg.data" />
    </div>

    <empty _Title='当前商品已下架' v-if="takeDownSale">
      <div class="sale-btn">
        <Button size="small" class="mr_10" @click="target('/')">返回首页</Button>
        <Button size="small" @click="target('goodsList')">返回商品列表</Button>
      </div>
    </empty>
    <Spin size="large" fix v-if="isLoading"></Spin>
    <BaseFooter></BaseFooter>
  </div>
</template>

<script>
import Search from "@/components/Search";
import BaseHeader from "@/components/header/Header.vue";
import BaseFooter from "@/components/footer/Footer.vue";
import empty from "@/components/empty/Main";
import ShowGoodsDetail from "@/components/goodsDetail/ShowGoodsDetail";
import GoodsImages from "@/components/goodsDetail/GoodsImages.vue";
import GoodsInfo from "@/components/goodsDetail/GoodsInfo.vue";
import StoreHeaderInfo from "@/components/goodsDetail/StoreHeaderInfo.vue";
import { goodsSkuDetail } from "@/api/goods";
import {
  cancelStoreCollect,
  collectStore,
  isStoreCollection,
  getGoodsDistribution,
  collectGoods,
  cancelCollect,
  isCollection,
  receiveCoupon,
} from "@/api/member";
import { getDetailById } from "@/api/shopentry";
import { addCartGoods } from "@/api/cart.js";
import imTalk from '@/components/mixes/talkIm'
export default {
  name: "GoodsDetail",
  beforeRouteEnter (to, from, next) {
    window.scrollTo(0, 0);
    next();
  },
  created () {
    this.getGoodsDetail();
  },
  mixins: [imTalk],
  data () {
    return {
      goodsMsg: {},
      isLoading: false,
      categoryBar: [],
      storeCollected: false,
      storeMsg: {},
      takeDownSale:false,
      isProductCollected: false,
      currentSelection: [],
      currentCount: 1,
      formattedSpecList: [],
      promotions: { SECKILL: null, FULL_DISCOUNT: null, COUPON: [] },
      addingToCart: false,
      buyingNow: false,
      pointPaying: false,
    };
  },
  methods: {
    target(url){
      this.$router.push({path: url})
    },
    targetClickSku (val) {
      this.getGoodsDetail(val);
    },
    getGoodsDetail (val) {
      this.isLoading = true;
      const params = val || this.$route.query;

      let distributionId =
        params && params.distributionId
          ? params.distributionId
          : this.Cookies.getItem("distributionId");
      if (distributionId) {
        console.log(distributionId);
        this.Cookies.setItem("distributionId", params.distributionId);
        let _this = this;
        getGoodsDistribution(params.distributionId).then((res) => {
          if (res.success) {
            _this.Cookies.removeItem("distributionId");
          }
        });
      }

      goodsSkuDetail(params)
        .then((res) => {
          this.isLoading = false;
          if (res.success) {
            this.goodsMsg = res.result;

            // --- Start: Data Formatting for Child Components ---
            const cateName = this.goodsMsg.categoryName;
            const cateId = this.goodsMsg.data.categoryPath ? this.goodsMsg.data.categoryPath.split(",") : [];
            const cateArr = [];
            cateId.forEach((e, index) => {
              cateArr.push({
                id: e,
                name: cateName ? cateName[index] : "",
              });
            });
            this.categoryBar = cateArr;

            // Format Specs - Uses goodsMsg.specs (res.result.specs)
            this.formatSpecsForInfo(this.goodsMsg.specs);

            // Initialize Selection - Uses goodsMsg.specs and goodsMsg.data.id (res.result.specs, res.result.data.id)
            this.initializeCurrentSelection(this.goodsMsg.specs, this.goodsMsg.data.id);

            // Format Promotions - Uses goodsMsg.promotionMap (res.result.promotionMap)
            this.formatPromotionsForInfo(this.goodsMsg.promotionMap);

            if (!this.goodsMsg.data.intro) {
              this.goodsMsg.data.intro = ''
            }
            if (this.Cookies.getItem("userInfo")) {
              isStoreCollection("STORE", this.goodsMsg.data.storeId).then((storeRes) => {
                this.storeCollected = !!(storeRes.success && storeRes.result);
              });
              this.checkProductCollectionStatus();
            }

            if (!this.storeMsg.id || this.storeMsg.id !== this.goodsMsg.data.storeId) {
              getDetailById(this.goodsMsg.data.storeId).then((storeRes) => {
                 if (storeRes.success && storeRes.result) {
                    this.$set(this, 'storeMsg', storeRes.result);
                 } else {
                    this.$set(this, 'storeMsg', {});
                 }
              }).catch((err) => {
                  this.$set(this, 'storeMsg', {});
              });
            } else {
              // REMOVED: console.log('Store details already loaded for storeId:', this.storeMsg.id);
            }

            document.title = this.goodsMsg.data.goodsName;
          } else {
            this.$Message.error(res.message);
            this.isLoading = false
            if(res.code === 11001){ this.takeDownSale = true }
          }
        })
        .catch((e) => {
          this.isLoading = false
          if(e && e.code === 11001){
            this.takeDownSale = true
          }
        });
    },
    goGoodsList (currIndex) {
      const arr = [];
      this.categoryBar.forEach((e, index) => {
        if (index <= currIndex) { arr.push(e.id); }
      });
      return location.origin + "/goodsList?categoryId=" + arr.toString();
    },
    async collect () {
      if (this.storeCollected) {
        let cancel = await cancelStoreCollect("STORE", this.goodsMsg.data.storeId);
        if (cancel.success) {
          this.$Message.success("已取消收藏");
          this.storeCollected = false;
        }
      } else {
        let collect = await collectStore("STORE", this.goodsMsg.data.storeId);
        if (collect.code === 200) {
          this.storeCollected = true;
          this.$Message.success("收藏店铺成功");
        }
      }
    },
    extractImageList(skuData) {
      if (skuData && skuData.goodsGalleryList) {
        return skuData.goodsGalleryList.filter(img => typeof img === 'string' && img.trim() !== '');
      } else if (skuData && skuData.original) {
        return [skuData.original];
      }
      return [];
    },
    formatSpecsForInfo(apiSpecs) {
      if (!apiSpecs || !Array.isArray(apiSpecs)) {
        this.formattedSpecList = [];
        return;
      }
      let specMap = {};
      apiSpecs.forEach(sku => {
        if (sku.specValues && Array.isArray(sku.specValues)) {
          sku.specValues.forEach(spec => {
            if (spec.specName && spec.specName !== 'images') {
              if (!specMap[spec.specName]) {
                specMap[spec.specName] = { name: spec.specName, valuesSet: new Set() };
              }
              specMap[spec.specName].valuesSet.add(spec.specValue);
            }
          });
        }
      });
      this.formattedSpecList = Object.values(specMap).map(group => ({
        name: group.name,
        values: Array.from(group.valuesSet).map(val => ({ value: val, disabled: false }))
      }));
    },
    initializeCurrentSelection(apiSpecs, currentSkuId) {
      if (!apiSpecs || !Array.isArray(apiSpecs) || !currentSkuId || this.formattedSpecList.length === 0) {
        this.currentSelection = this.formattedSpecList.map(() => undefined);
        return;
      }
      const currentApiSku = apiSpecs.find(s => s.skuId === currentSkuId);
      if (currentApiSku && currentApiSku.specValues) {
        this.currentSelection = this.formattedSpecList.map(fmtSpec => {
          const apiVal = currentApiSku.specValues.find(v => v.specName === fmtSpec.name);
          return apiVal ? apiVal.specValue : undefined;
        });
      } else {
        this.currentSelection = this.formattedSpecList.map(() => undefined);
      }
    },
    formatPromotionsForInfo(apiPromotionMap) {
      const promoData = { SECKILL: null, FULL_DISCOUNT: null, COUPON: [] };
      if (!apiPromotionMap) {
        this.promotions = promoData;
        return;
      }
      Object.keys(apiPromotionMap).forEach(key => {
        const type = key.split('-')[0];
        if (promoData.hasOwnProperty(type)) {
          if (type === 'COUPON') {
            promoData.COUPON.push(apiPromotionMap[key]);
          } else {
            promoData[type] = apiPromotionMap[key];
          }
        }
      });
      promoData.COUPON.sort((a, b) => (a.consumeThreshold || 0) - (b.consumeThreshold || 0));
      this.promotions = promoData;
    },
    checkProductCollectionStatus() {
      if (this.Cookies.getItem("userInfo") && this.goodsMsg.data && this.goodsMsg.data.id) {
        isCollection("GOODS", this.goodsMsg.data.id).then((res) => {
          this.isProductCollected = !!(res.success && res.result);
        }).catch(() => { this.isProductCollected = false; });
      } else {
        this.isProductCollected = false;
      }
    },
    handleSkuSelect({ specIndex, value }) {
      let newSelection = [...this.currentSelection];
      this.$set(newSelection, specIndex, value);
      this.currentSelection = newSelection;

      const targetSku = this.findSkuBySelection(this.goodsMsg.specs, this.currentSelection);

      if (targetSku && targetSku.skuId !== this.goodsMsg.data.id) {
        this.targetClickSku({ skuId: targetSku.skuId, goodsId: this.goodsMsg.data.goodsId });
      } else if (!targetSku) {
        console.warn("Selected specification combination does not match any SKU.");
      }
    },
    findSkuBySelection(apiSpecs, selection) {
      if (!apiSpecs || !Array.isArray(apiSpecs) || selection.includes(undefined)) {
        return null;
      }
      return apiSpecs.find(sku => {
        if (!sku.specValues || !Array.isArray(sku.specValues)) return false;
        const relevantSpecs = sku.specValues.filter(sv => sv.specName !== 'images');
        if (relevantSpecs.length !== selection.length) return false;
        return relevantSpecs.every((sv, index) => sv.specValue === selection[index]);
      });
    },
    handleCountChange(count) {
      this.currentCount = count;
    },
    handleAddToCart() {
      if (!this.goodsMsg.data || !this.goodsMsg.data.id) return;
      const params = {
        num: this.currentCount,
        skuId: this.goodsMsg.data.id,
      };
      this.addingToCart = true;
      addCartGoods(params)
        .then((res) => {
          if (res.success) {
            this.$router.push({ path: "/shoppingCart" });
          } else {
            this.$Message.warning(res.message || "加入购物车失败");
          }
        })
        .catch(() => { this.$Message.error("网络错误，请稍后再试"); })
        .finally(() => { this.addingToCart = false; });
    },
    handleBuyNow() {
      if (!this.goodsMsg.data || !this.goodsMsg.data.id) return;
      const params = {
        num: this.currentCount,
        skuId: this.goodsMsg.data.id,
        cartType: this.goodsMsg.data.goodsType === "VIRTUAL_GOODS" ? "VIRTUAL" : "BUY_NOW",
      };
      this.buyingNow = true;
      addCartGoods(params)
        .then((res) => {
          if (res.success) {
            this.$router.push({ path: "/pay", query: { way: params.cartType } });
          } else {
            this.$Message.warning(res.message || "立即购买失败");
          }
        })
        .catch(() => { this.$Message.error("网络错误，请稍后再试"); })
        .finally(() => { this.buyingNow = false; });
    },
    handlePointPay() {
      console.log("Point pay logic needs implementation");
      this.$Message.info("积分支付功能待实现");
    },
    async handleCollectProduct() {
      if (!this.goodsMsg.data || !this.goodsMsg.data.id) return;
      const skuId = this.goodsMsg.data.id;
      if (this.isProductCollected) {
        try {
          let res = await cancelCollect("GOODS", skuId);
          if (res.success) {
            this.$Message.success("取消收藏成功");
            this.isProductCollected = false;
          }
        } catch (e) { this.$Message.error("操作失败"); }
      } else {
        try {
          let res = await collectGoods("GOODS", skuId);
          if (res.code === 200 || res.success) {
            this.$Message.success("收藏成功");
            this.isProductCollected = true;
          } else {
            this.$Message.warning(res.message || "收藏失败");
          }
        } catch (e) { this.$Message.error("操作失败"); }
      }
    },
    handleCouponReceived(couponId) {
      console.log("Coupon received:", couponId);
    },
  },
  watch: {
    '$route.query': {
      handler(newQuery, oldQuery) {
        if (newQuery.skuId !== oldQuery.skuId || newQuery.goodsId !== oldQuery.goodsId) {
          this.getGoodsDetail();
        }
      },
    },
  },
  components: {
    Search,
    ShowGoodsDetail,
    GoodsImages,
    GoodsInfo,
    empty,
    BaseHeader,
    BaseFooter,
    StoreHeaderInfo
  },
  computed: {
      formattedDeliveryTime() {
          return this.storeMsg && this.storeMsg.deliveryScore ? `发货: ${this.storeMsg.deliveryScore}` : '发货: N/A';
      },
      formattedResponseTime() {
          return this.storeMsg && this.storeMsg.serviceScore ? `响应: ${this.storeMsg.serviceScore}` : '响应: N/A';
      },
      formattedLogisticsRating() {
           return this.storeMsg && this.storeMsg.logisticsScore ? `物流: ${this.storeMsg.logisticsScore}` : '物流: N/A';
      }
  }
};
</script>
<style scoped lang="scss">
.shop-item-path {
  height: 38px;
  @include background_color($light_background_color);
  line-height: 38px;
  color: #2c2c2c;
  border-bottom: 1px solid #eee;
}

.shop-nav-container {
  width: 1200px;
  margin: 0 auto;
  position: relative;

  .store-collect {
    position: absolute;
    right: 20px;
    top: 0;
    color: #999;

    span {
      &:hover {
        cursor: pointer;
        color: $theme_color;
      }
    }
  }
}
.sale-btn{
  margin:10px 0
}

/* New Layout Styles */
.goods-detail-main-container {
  width: 1200px;
  margin: 20px auto;
  display: flex;
  align-items: flex-start;
  gap: 20px;
}

.left-scroll-container {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.right-sticky-container {
  width: 480px;
  flex-shrink: 0;
  position: sticky;
  top: 10px;
}

.left-scroll-container > * {
  width: 100%;
}

/* New Layout Styles */
.store-header-wrapper {
  width: 1200px;
  margin: 0 auto 20px auto; /* Top margin 0, horizontal auto, bottom margin 20px */
}
</style>
