import request, {Method, commonUrl} from '@/plugins/request.js';

/**
 * 获取拼图验证
 */
export function getVerifyImg (verificationEnums) {
  return request({
    url: `${commonUrl}/common/common/slider/${verificationEnums}`,
    method: Method.GET,
    needToken: false
  });
}
/**
 * 验证码校验
 */
export function postVerifyImg (params) {
  return request({
    url: `${commonUrl}/common/common/slider/${params.verificationEnums}`,
    method: Method.POST,
    needToken: false,
    params
  });
}
/**
 * 发送短信验证码
 */
export function sendSms (params) {
  return request({
    url: `${commonUrl}/common/common/sms/${params.verificationEnums}/${params.mobile}`,
    method: Method.GET,
    needToken: false
  });
}

// 地区数据，用于三级联动
export function getRegion (id) {
  return request({
    url: `${commonUrl}/common/common/region/item/${id}`,
    needToken: true,
    method: Method.GET
  });
}

/**
 * 分页获取文章列表
 * @param cateId 文章分类id
 */
export function articleList (params) {
  return request({
    url: `/buyer/other/article`,
    method: Method.GET,
    params
  });
}

/**
 * 获取帮助中心文章分类列表
 * @param cateId 文章分类id
 */
export function articleCateList () {
  return request({
    url: `/buyer/other/article/articleCategory/list`,
    method: Method.GET
  });
}

// 通过id获取文章
export function articleDetail (id) {
  return request({
    url: `/buyer/other/article/get/${id}`,
    method: Method.GET
  });
}

/**
 * 营业执照OCR识别
 * @param file 营业执照图片文件
 */
export function recognizeBusinessLicense (file) {
  const formData = new FormData();
  formData.append('file', file);
  
  return request({
    url: `${commonUrl}/common/common/ocr/business/license`,
    method: Method.POST,
    data: formData, // Use 'data' for POST requests with FormData
    headers: {
      'Content-Type': 'multipart/form-data' // Explicitly set content type
    },
    needToken: true, // 确保携带Token
    timeout: 30000 // 增加超时时间到30秒
  });
}

// 获取IM接口前缀
export function getIMDetail () {
  return request({
    url: `${commonUrl}/common/common/IM`,
    method: Method.GET
  });
}

//获取图片logo
export function getBaseSite(){
  return request ({
    url:`${commonUrl}/common/common/site`,
    method: Method.GET,
    needToken: false
  })
}

/**
 * 身份证OCR识别
 * @param file 身份证图片文件（正面或反面）
 * @param type 身份证类型，'front'表示正面，'back'表示反面
 */
export function recognizeIDCard(file, type = 'front') {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('type', type);
  
  return request({
    url: `${commonUrl}/common/common/ocr/idcard`,
    method: Method.POST,
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    needToken: true,
    timeout: 30000
  });
}

/**
 * 身份证正面OCR识别
 * @param file 身份证正面图片文件
 */
export function recognizeIDCardFront(file) {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('type', 'front');
  
  return request({
    url: `${commonUrl}/common/common/ocr/idcard/front`,
    method: Method.POST,
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    needToken: true,
    timeout: 30000
  });
}

/**
 * 身份证反面OCR识别
 * @param file 身份证反面图片文件
 */
export function recognizeIDCardBack(file) {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('type', 'back');
  
  return request({
    url: `${commonUrl}/common/common/ocr/idcard/back`,
    method: Method.POST,
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    needToken: true,
    timeout: 30000
  });
}
