<template>
  <div class="image-magnifier-container">
    <!-- 主图区域 -->
    <div
      class="main-image-wrapper"
      @mouseenter="handleMouseEnter"
      @mouseleave="handleMouseLeave"
      @mousemove="handleMouseMove"
      ref="imageWrapper"
    >
      <img
        :src="imageUrl"
        :alt="alt"
        class="main-image"
        ref="mainImage"
        @load="handleImageLoad"
      />

      <!-- 鼠标跟踪框 -->
      <div
        v-if="showMagnifier && isImageLoaded"
        class="magnifier-lens"
        :style="lensStyle"
      ></div>
    </div>

    <!-- 放大镜显示区域 -->
    <div
      v-if="showMagnifier && isImageLoaded"
      class="magnifier-display"
      :style="magnifierStyle"
      ref="magnifierDisplay"
    >
      <img
        :src="imageUrl"
        class="magnified-image"
        :style="magnifiedImageStyle"
        ref="magnifiedImage"
      />
    </div>
  </div>
</template>

<script>
export default {
  name: 'ImageMagnifier',
  props: {
    imageUrl: {
      type: String,
      required: true
    },
    alt: {
      type: String,
      default: ''
    },
    magnifierSize: {
      type: Number,
      default: 300
    },
    zoomLevel: {
      type: Number,
      default: 2
    },
    lensSize: {
      type: Number,
      default: 100
    }
  },
  data() {
    return {
      showMagnifier: false,
      isImageLoaded: false,
      canUseMagnifier: false,
      mouseX: 0,
      mouseY: 0,
      imageRect: null,
      imageNaturalWidth: 0,
      imageNaturalHeight: 0
    };
  },
  computed: {
    // 镜头样式
    lensStyle() {
      if (!this.imageRect) return {};

      const lensX = this.mouseX - this.lensSize / 2;
      const lensY = this.mouseY - this.lensSize / 2;

      // 限制镜头在图片范围内
      const maxX = this.imageRect.width - this.lensSize;
      const maxY = this.imageRect.height - this.lensSize;

      const constrainedX = Math.max(0, Math.min(lensX, maxX));
      const constrainedY = Math.max(0, Math.min(lensY, maxY));

      return {
        left: constrainedX + 'px',
        top: constrainedY + 'px',
        width: this.lensSize + 'px',
        height: this.lensSize + 'px'
      };
    },

    // 放大镜容器样式
    magnifierStyle() {
      if (!this.imageRect) return {};

      // 计算放大镜位置：图片右侧
      const left = this.imageRect.right + 10;
      const top = this.imageRect.top;

      // 检查是否超出屏幕右边界
      const screenWidth = window.innerWidth;
      const finalLeft = (left + this.magnifierSize > screenWidth)
        ? screenWidth - this.magnifierSize - 20
        : left;

      return {
        position: 'fixed',
        left: finalLeft + 'px',
        top: top + 'px',
        width: this.magnifierSize + 'px',
        height: this.magnifierSize + 'px',
        zIndex: 9999
      };
    },

    // 放大图片样式
    magnifiedImageStyle() {
      if (!this.imageRect) return {};

      const lensX = this.mouseX - this.lensSize / 2;
      const lensY = this.mouseY - this.lensSize / 2;

      // 限制镜头在图片范围内
      const maxX = this.imageRect.width - this.lensSize;
      const maxY = this.imageRect.height - this.lensSize;

      const constrainedX = Math.max(0, Math.min(lensX, maxX));
      const constrainedY = Math.max(0, Math.min(lensY, maxY));

      // 计算放大图片的偏移
      const scaleX = this.imageNaturalWidth / this.imageRect.width;
      const scaleY = this.imageNaturalHeight / this.imageRect.height;

      const offsetX = -constrainedX * scaleX * this.zoomLevel;
      const offsetY = -constrainedY * scaleY * this.zoomLevel;

      const imageWidth = this.imageNaturalWidth * this.zoomLevel;
      const imageHeight = this.imageNaturalHeight * this.zoomLevel;

      return {
        width: imageWidth + 'px',
        height: imageHeight + 'px',
        transform: `translate(${offsetX}px, ${offsetY}px)`
      };
    }
  },
  methods: {
    handleImageLoad() {
      this.isImageLoaded = true;
      const img = this.$refs.mainImage;
      this.imageNaturalWidth = img.naturalWidth;
      this.imageNaturalHeight = img.naturalHeight;

      // 只有图片足够大才启用放大镜
      const minSize = 300;
      this.canUseMagnifier = this.imageNaturalWidth >= minSize && this.imageNaturalHeight >= minSize;
    },

    handleMouseEnter() {
      if (!this.canUseMagnifier) return;

      this.showMagnifier = true;
      this.updateImageRect();
    },

    handleMouseLeave() {
      this.showMagnifier = false;
    },

    handleMouseMove(event) {
      if (!this.showMagnifier || !this.imageRect) return;

      const rect = this.$refs.imageWrapper.getBoundingClientRect();
      this.mouseX = event.clientX - rect.left;
      this.mouseY = event.clientY - rect.top;
    },

    updateImageRect() {
      if (this.$refs.imageWrapper) {
        this.imageRect = this.$refs.imageWrapper.getBoundingClientRect();
      }
    }
  },
  mounted() {
    // 监听窗口大小变化
    window.addEventListener('resize', this.updateImageRect);
    window.addEventListener('scroll', this.updateImageRect);
  },

  beforeDestroy() {
    window.removeEventListener('resize', this.updateImageRect);
    window.removeEventListener('scroll', this.updateImageRect);
  }
};
</script>

<style scoped lang="scss">
.image-magnifier-container {
  position: relative;
  display: inline-block;
}

.main-image-wrapper {
  position: relative;
  cursor: crosshair;
  overflow: hidden;

  .main-image {
    display: block;
    width: 100%;
    height: 100%;
    object-fit: contain;
  }
}

.magnifier-lens {
  position: absolute;
  border: 2px solid #fff;
  box-shadow: 0 0 0 1px rgba(0,0,0,0.3);
  background-color: rgba(255,255,255,0.3);
  pointer-events: none;
  z-index: 10;
}

.magnifier-display {
  border: 1px solid #ddd;
  border-radius: 6px;
  background-color: #fff;
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
  overflow: hidden;

  .magnified-image {
    display: block;
    pointer-events: none;
  }
}

// 响应式处理：在小屏幕上隐藏放大镜
@media (max-width: 1240px) {
  .magnifier-display {
    display: none !important;
  }
}
</style>
