<template>
  <div class="goods-images-container">
    <!-- 垂直缩略图列表 (移到左侧) -->
    <div class="thumbnail-list">
      <!-- 视频缩略图 (如果存在视频) -->
      <div
        v-if="videoUrl"
        class="thumbnail-item video-thumbnail"
        :class="{ active: showVideo }"
        @click="playVideo"
      >
        <img :src="thumbnailUrl || (imageList.length > 0 ? imageList[0] : '')" alt="视频缩略图" />
        <div class="thumbnail-play-icon">
          <Icon type="ios-play" size="16" color="#fff" /> <!-- Smaller icon -->
        </div>
      </div>
      <!-- 图片缩略图 -->
      <div
        v-for="(image, index) in imageList"
        :key="index"
        class="thumbnail-item image-thumbnail"
        :class="{ active: !showVideo && activeIndex === index }"
        @mouseover="showImage(index)"
      >
        <img :src="image.url || image" :alt="`商品图片 ${index + 1}`" />
      </div>
       <!-- 可以添加上下滚动箭头，如果缩略图过多 -->
    </div>

    <!-- 大图区域 (移到右侧) -->
    <div
      class="main-image-area"
      @mouseenter="handleMainImageMouseEnter"
      @mouseleave="handleMainImageMouseLeave"
    >
      <template v-if="!showVideo && currentImageUrl">
        <pic-zoom
          v-if="isImageLargeEnoughForZoom && showZoomer"
          :url="currentImageUrl"
          :scale="2"
        ></pic-zoom>
        <img
          v-else
          :src="currentImageUrl"
          alt="商品主图"
          class="static-main-image"
        />
      </template>
      <!-- 视频播放器 -->
      <div v-if="showVideo && videoUrl" class="video-player-wrapper">
        <div :id="dplayerId" class="dplayer-instance"></div>
        <div class="video-close-button" @click="closeVideo">
          <Icon type="md-close" size="24" color="#fff" />
        </div>
      </div>
      <!-- 视频播放覆盖层 -->
       <div
        v-if="!showVideo && videoUrl && activeIndex === 0"
        class="video-overlay"
        @click="playVideo"
       >
        <div class="play-button-icon">
          <Icon type="ios-play" size="36" color="#fff" />
        </div>
      </div>
       <!-- 加载或无图提示 -->
       <div v-else-if="!currentImageUrl && !showVideo" class="no-image-placeholder">
         暂无图片
       </div>
    </div>

     <!-- 收藏按钮已移至 GoodsInfo -->
  </div>
</template>

<script>
 import PicZoom from "vue-piczoom";
import DPlayer from "dplayer";

export default {
  name: "GoodsImages",
  components: { PicZoom },
  props: {
    imageList: {
      type: Array,
      default: () => [],
    },
    videoUrl: {
      type: String,
      default: null,
    },
    thumbnailUrl: {
       type: String,
       default: ''
    },
  },
  data() {
    return {
      dplayerId: 'dplayer-' + Math.random().toString(36).substr(2, 9),
      activeIndex: 0,
      showVideo: false,
      playerInstance: null,
      isImageLargeEnoughForZoom: false, // Default to false
      showZoomer: false, // <-- New property
      magnifierPositioned: false, // 标记放大镜是否已定位
      positionMonitorTimer: null // 位置监控定时器
    };
  },
  computed: {
    currentImageUrl() {
        if (this.imageList && this.imageList.length > this.activeIndex && this.imageList[this.activeIndex]) {
            const item = this.imageList[this.activeIndex];
            return item.url || item;
        }
        return null;
    },
    hasVideo() {
      return !!this.videoUrl;
    }
  },
  watch: {
    videoUrl(newVal, oldVal) {
      if (!newVal && oldVal) {
        this.closeVideo();
        this.showVideo = false;
      }
    },
    imageList() {
        this.activeIndex = 0;
        if(this.showVideo && !this.hasVideo) {
             this.closeVideo();
        }
        // imageList 变化也可能导致 currentImageUrl 变化, 触发其 watcher
    },
    currentImageUrl: {
      handler(newUrl) {
        if (newUrl) {
          const img = new Image();
          img.onload = () => {
            // 设置合理的最小尺寸阈值，适合放大镜功能
            // 降低阈值以适应更多图片尺寸
            const minWidth = 300;
            const minHeight = 300;
            this.isImageLargeEnoughForZoom = img.naturalWidth >= minWidth && img.naturalHeight >= minHeight;
            if (!this.isImageLargeEnoughForZoom) {
              this.showZoomer = false; // Ensure zoomer is hidden if image not suitable
            }
            // 如果图片足够大且鼠标在图片区域内，显示放大镜
            // 注意：这里不自动显示，等待鼠标悬停事件
          };
          img.onerror = () => {
            this.isImageLargeEnoughForZoom = false;
            this.showZoomer = false; // Ensure zoomer is hidden on error
          };
          img.src = newUrl;
        } else {
          this.isImageLargeEnoughForZoom = false;
          this.showZoomer = false; // Ensure zoomer is hidden if no URL
        }
      },
      immediate: true, // Call handler immediately when component is created
    }
  },
  mounted() {
    // REMOVED: console.log('[GoodsImages] Component mounted.');
  },
  beforeDestroy() {
    // 清理定时器
    this.stopPositionMonitor();
  },
  methods: {
    showImage(index) {
      if (this.showVideo) {
        this.closeVideo();
        this.showVideo = false;
      }
      this.activeIndex = index;
    },
    handleMainImageMouseEnter() {
      // Only show zoomer if the image is large enough
      if (this.isImageLargeEnoughForZoom) {
        this.showZoomer = true;
        // 重写vue-piczoom的定位逻辑
        this.$nextTick(() => {
          this.positionMagnifier();
        });
      }
    },
    handleMainImageMouseLeave() {
      this.showZoomer = false;
      // 停止位置监控
      this.stopPositionMonitor();
    },
    // 动态定位放大镜到图片右侧
    positionMagnifier() {
      // 等待放大镜画布创建，增加延迟时间
      setTimeout(() => {
        // 尝试多种可能的选择器来找到放大镜元素
        const selectors = [
          'body > canvas.mouse-cover-canvas',
          'canvas.mouse-cover-canvas',
          '.pic-zoom canvas',
          '.pic-zoom-container canvas',
          'canvas[style*="position"]',
          'body > canvas',
          '.pic-zoom .zoom-canvas'
        ];

        let magnifierElement = null;

        // 逐一尝试选择器
        for (const selector of selectors) {
          magnifierElement = document.querySelector(selector);
          if (magnifierElement) {
            console.log(`找到放大镜元素，使用选择器: ${selector}`);
            break;
          }
        }

        // 如果还是找不到，尝试查找所有canvas元素
        if (!magnifierElement) {
          const allCanvases = document.querySelectorAll('canvas');
          console.log(`找到 ${allCanvases.length} 个canvas元素`);

          // 查找最近创建的canvas（通常是放大镜）
          if (allCanvases.length > 0) {
            magnifierElement = allCanvases[allCanvases.length - 1];
            console.log('使用最后一个canvas作为放大镜元素');
          }
        }

        if (magnifierElement) {
          const mainImageArea = this.$el.querySelector('.main-image-area');
          if (mainImageArea) {
            const rect = mainImageArea.getBoundingClientRect();
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

            // 计算放大镜位置 (使用fixed定位，不需要加scrollTop)
            const magnifierLeft = rect.right + 10; // 图片右侧 + 10px间距
            const magnifierTop = rect.top; // 与图片顶部对齐 (fixed定位相对于viewport)
            const magnifierSize = Math.min(rect.width, rect.height); // 与图片相同大小

            // 检查是否超出屏幕右边界
            const screenWidth = window.innerWidth;
            const finalLeft = (magnifierLeft + magnifierSize > screenWidth)
              ? screenWidth - magnifierSize - 20 // 如果超出，则贴右边界
              : magnifierLeft;

            console.log(`设置放大镜位置: left=${finalLeft}, top=${magnifierTop}, size=${magnifierSize}`);

            // 强制设置位置并启动持续监控
            this.startPositionMonitor(magnifierElement, finalLeft, magnifierTop, magnifierSize);

            this.magnifierPositioned = true;
          }
        } else {
          console.log('未找到放大镜元素');
        }
      }, 300); // 增加延迟到300ms确保画布已创建
    },

    // 强制应用放大镜位置样式
    applyMagnifierPosition(element, left, top, size) {
      // 使用transform强制移动，优先级更高
      element.style.position = 'fixed';
      element.style.transform = `translate(${left}px, ${top}px)`;
      element.style.left = '0px';
      element.style.top = '0px';
      element.style.width = size + 'px';
      element.style.height = size + 'px';
      element.style.zIndex = '9999';

      console.log(`应用transform: translate(${left}px, ${top}px)`);
    },

    // 启动位置监控
    startPositionMonitor(element, targetLeft, targetTop, targetSize) {
      // 停止之前的监控
      this.stopPositionMonitor();

      // 立即设置一次位置
      this.applyMagnifierPosition(element, targetLeft, targetTop, targetSize);

      // 启动定时监控，每100ms检查一次
      this.positionMonitorTimer = setInterval(() => {
        if (element && element.parentNode) {
          // 检查transform是否被改变
          const currentTransform = element.style.transform || '';
          const expectedTransform = `translate(${targetLeft}px, ${targetTop}px)`;

          // 检查left/top是否被重新设置（vue-piczoom会重新设置这些值）
          const currentLeft = parseInt(element.style.left) || 0;
          const currentTop = parseInt(element.style.top) || 0;

          if (currentTransform !== expectedTransform || currentLeft !== 0 || currentTop !== 0) {
            console.log(`检测到放大镜位置被改变，重新应用transform`);
            this.applyMagnifierPosition(element, targetLeft, targetTop, targetSize);
          }
        } else {
          // 如果元素被移除，停止监控
          this.stopPositionMonitor();
        }
      }, 100);
    },

    // 停止位置监控
    stopPositionMonitor() {
      if (this.positionMonitorTimer) {
        clearInterval(this.positionMonitorTimer);
        this.positionMonitorTimer = null;
      }
    },
    playVideo() {
      if (!this.videoUrl) return;
      if(!this.showVideo){
         this.activeIndex = 0;
      }
      this.showVideo = true;
      this.$nextTick(() => {
        this.initializePlayer();
      });
    },
    closeVideo() {
      if (this.playerInstance) {
        this.playerInstance.pause();
        this.playerInstance.destroy();
        this.playerInstance = null;
      }
      this.showVideo = false;
    },
    initializePlayer() {
      if (!this.videoUrl) return;
      const container = document.getElementById(this.dplayerId);
      if (!container) {
          console.error(`DPlayer container not found: #${this.dplayerId}`);
          return;
      }
      if (!this.playerInstance) {
        this.playerInstance = new DPlayer({
          container: container,
          video: {
            url: this.videoUrl,
          },
          autoplay: true,
          theme: "#ed3f14",
          preload: "auto",
          volume: 0.7,
          contextmenu: [
            {
              text: "商品视频",
              link: "#",
            },
          ],
        });
      } else {
         if (this.playerInstance.video.currentSrc !== this.videoUrl) {
           this.playerInstance.switchVideo({ url: this.videoUrl });
         }
         this.playerInstance.play();
      }
    },
  },
  beforeDestroy() {
    if (this.playerInstance) {
      this.playerInstance.destroy();
      this.playerInstance = null;
    }
  },
};
</script>

<style scoped lang="scss">
.goods-images-container {
  display: flex;
  flex-direction: row;
  width: 100%; /* Fill the parent .left-scroll-container (720px) */
  gap: 10px;
}


/* Vertical Thumbnail List Styles */
.thumbnail-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
  width: 80px; /* Increased thumbnail width */
  flex-shrink: 0;
  max-height: 500px; /* Increase max-height as the container is wider now */
  overflow-y: auto;
}

.thumbnail-item {
  width: 80px; /* Increased thumbnail width */
  height: 80px; /* Increased thumbnail height */
  border: 1px solid #ddd;
  cursor: pointer;
  position: relative;
  overflow: hidden; /* Ensure content respects border-radius */
  background-color: #fff;
  flex-shrink: 0; /* Prevent items from shrinking */
  border-radius: 4px; /* Add border-radius */

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
  }

  &.active {
    border-color: $theme_color;
    box-shadow: 0 0 0 1px $theme_color; /* Add inset shadow effect */
  }

  &:hover {
     border-color: lighten($theme_color, 20%);
  }
}

.video-thumbnail {
  &:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.3);
    z-index: 1;
    transition: background-color 0.2s ease;
  }

  &:hover::before {
     background-color: rgba(0, 0, 0, 0.1);
  }

  &.active::before {
     background-color: rgba(0, 0, 0, 0);
  }
}

.thumbnail-play-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 20px; /* Smaller icon background */
  height: 20px;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2;
  pointer-events: none;
}


.main-image-area {
  flex-grow: 1;
  position: relative;
  overflow: hidden;
  aspect-ratio:  1/ 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f8f8f8;
  border-radius: 6px;
}

.static-main-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.video-player-wrapper {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #000;
  z-index: 10;
}

.dplayer-instance {
  width: 100%;
  height: 100%;
}

.video-close-button {
  position: absolute;
  top: 10px;
  right: 10px;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  z-index: 15;

  &:hover {
    background-color: rgba(0, 0, 0, 0.7);
  }
}

.video-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.3);
  cursor: pointer;
  z-index: 5;
}

.play-button-icon {
  width: 70px;
  height: 70px;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: rgba(0, 0, 0, 0.8);
  }
}

.no-image-placeholder {
   color: #999;
   font-size: 16px;
}

</style>

<style lang="scss">
// 放大镜样式 - 由JavaScript动态定位到图片右侧
body > canvas.mouse-cover-canvas {
  background-color: #ffffff !important; /* 保持白色背景 */
  border: 1px solid #ddd !important; /* 添加边框 */
  box-shadow: 0 2px 8px rgba(0,0,0,0.15) !important; /* 添加阴影 */
  z-index: 9999 !important; /* 确保在最上层 */
  border-radius: 6px !important; /* 圆角 */

  // 位置和尺寸由JavaScript动态设置
  // 不在这里设置 left, top, width, height
}

// 响应式处理：在小屏幕上隐藏放大镜
@media (max-width: 1240px) {
  body > canvas.mouse-cover-canvas {
    display: none !important;
  }
}
</style>