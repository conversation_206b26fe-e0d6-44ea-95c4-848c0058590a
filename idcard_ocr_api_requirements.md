# 身份证OCR识别功能需求文档

## 1. 功能目标

实现法人身份证信息的自动识别与填充，包括姓名、身份证号、出生日期、性别、民族、住址、签发机关以及身份证有效期。为达到此目标，前端将提供身份证正面和反面两个独立的图片上传入口。

## 2. 后端API需求

### API端点： `/common/common/ocr/idcard` (或其他商定路径)

*   **请求方法**: POST
*   **Content-Type**: `multipart/form-data`
*   **请求参数**:
    *   `file`: 身份证图片文件 (单张，正面或反面)
*   **需要Token验证**: 是
*   **超时时间建议**: 30秒

### 成功响应 (HTTP Status 200)

API应能根据上传的是正面还是反面图片，返回对应能识别出的信息。

**响应体 (JSON)**:
```json
{
  "success": true,
  "message": "识别成功",
  "code": 200,
  "result": {
    // 以下字段根据图片内容条件返回
    // 如果是正面，通常包含:
    "name": "张三",                 // 姓名
    "idNumber": "110101199001011234", // 身份证号
    "gender": "男",                  // 性别
    "ethnicity": "汉",               // 民族
    "birthDate": "1990-01-01",       // 出生日期 (格式: YYYY-MM-DD)
    "address": "北京市XX区XX街道XX号", // 住址

    // 如果是反面，通常包含:
    "issuingAuthority": "XX市公安局XX分局", // 签发机关
    "validFrom": "2020-01-01",           // 有效期起 (格式: YYYY-MM-DD)
    "validTo": "2030-01-01"              // 有效期止 (格式: YYYY-MM-DD 或 "长期")
  }
}
```
**字段说明**:
*   `name`: 姓名 (来自正面)
*   `idNumber`: 身份证号码 (来自正面)
*   `gender`: 性别 (来自正面)
*   `ethnicity`: 民族 (来自正面)
*   `birthDate`: 出生日期，格式 `YYYY-MM-DD` (来自正面)
*   `address`: 住址 (来自正面)
*   `issuingAuthority`: 签发机关 (来自反面)
*   `validFrom`: 有效期起始日期，格式 `YYYY-MM-DD` (来自反面)
*   `validTo`: 有效期截止日期，格式 `YYYY-MM-DD` 或字符串 "长期" (来自反面)

**重要**: 后端OCR服务在识别单张图片时，应仅返回该图片包含的信息。例如，如果上传的是正面，则 `result` 对象中不应包含 `issuingAuthority`, `validFrom`, `validTo` 字段，或者这些字段的值为 `null`。反之亦然。

### 失败响应 (HTTP Status 200，但业务失败，或 HTTP Status 4xx/5xx)

**响应体 (JSON)**:
```json
{
  "success": false,
  "message": "身份证识别失败: 图片不清晰或非身份证图片", // 具体的错误信息
  "code": 500, // 或其他业务错误码
  "result": null
}
```

## 3. OCR服务要求

*   OCR服务需要能够识别身份证的正面和反面。
*   能够从正面提取：姓名、性别、民族、出生年月日、公民身份号码、住址。
*   能够从反面提取：签发机关、有效期限。
*   对于日期字段 (`birthDate`, `validFrom`, `validTo`)，统一返回 `YYYY-MM-DD` 格式。如果有效期为长期，`validTo` 应返回字符串 "长期"。

## 4. 前端交互流程简述（供后端参考）

1.  前端界面将提供两个上传按钮："上传身份证正面"和"上传身份证反面"。
2.  用户点击"上传身份证正面"，选择正面图片后，前端将调用 `/common/common/ocr/idcard` API。
    *   API成功返回后，前端将使用返回的 `name`, `idNumber`, `gender`, `ethnicity`, `birthDate`, `address` 填充表单对应字段。
3.  用户点击"上传身份证反面"，选择反面图片后，前端将再次调用 `/common/common/ocr/idcard` API。
    *   API成功返回后，前端将使用返回的 `issuingAuthority`, `validFrom`, `validTo` 填充表单对应字段。
4.  前端会处理两次API调用的结果，合并到同一个表单数据对象中。

## 5. 注意事项

*   请确保API对于无法识别的图片（例如非身份证、模糊不清）能返回明确的错误提示。
*   日期格式的统一性非常重要。

请后端开发团队根据此需求文档实现API接口。完成后，请通知前端进行对接测试。 