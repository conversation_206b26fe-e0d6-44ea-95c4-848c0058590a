<template>
  <div class="layout">
    <Form ref="formValidate" :label-width="150" label-position="right" :model="formValidate" :rules="ruleValidate">
      
      <!-- OCR服务平台选择 -->
      <FormItem label="OCR服务平台" prop="ocrPlatform">
        <RadioGroup v-model="formValidate.ocrPlatform" type="button">
          <Radio label="ALI_OCR">阿里云OCR</Radio>
          <Radio label="BAIDU_OCR">百度OCR</Radio>
          <Radio label="TENCENT_OCR">腾讯云OCR</Radio>
        </RadioGroup>
        <span class="desc">选择您要使用的OCR图片识别服务平台</span>
      </FormItem>

      <!-- 阿里云OCR配置 -->
      <template v-if="formValidate.ocrPlatform === 'ALI_OCR'">
        <Divider orientation="left">阿里云OCR配置</Divider>
        <FormItem label="AccessKeyId" prop="aliOcrAccessKeyId">
          <Input v-model="formValidate.aliOcrAccessKeyId" placeholder="请输入阿里云OCR的AccessKeyId"/>
          <span class="desc">阿里云OCR服务的访问密钥ID</span>
        </FormItem>
        <FormItem label="AccessSecret" prop="aliOcrAccessSecret">
          <Input v-model="formValidate.aliOcrAccessSecret" type="password" placeholder="请输入阿里云OCR的AccessSecret"/>
          <span class="desc">阿里云OCR服务的访问密钥Secret</span>
        </FormItem>
        <FormItem label="Region" prop="aliOcrRegion">
          <Select v-model="formValidate.aliOcrRegion" placeholder="请选择区域">
            <Option value="cn-hangzhou">华东1（杭州）</Option>
            <Option value="cn-shanghai">华东2（上海）</Option>
            <Option value="cn-beijing">华北2（北京）</Option>
            <Option value="cn-shenzhen">华南1（深圳）</Option>
          </Select>
          <span class="desc">选择阿里云OCR服务的区域</span>
        </FormItem>
      </template>

      <!-- 百度OCR配置 -->
      <template v-if="formValidate.ocrPlatform === 'BAIDU_OCR'">
        <Divider orientation="left">百度OCR配置</Divider>
        <FormItem label="API Key" prop="baiduOcrApiKey">
          <Input v-model="formValidate.baiduOcrApiKey" placeholder="请输入百度OCR的API Key"/>
          <span class="desc">百度OCR服务的API Key</span>
        </FormItem>
        <FormItem label="Secret Key" prop="baiduOcrSecretKey">
          <Input v-model="formValidate.baiduOcrSecretKey" type="password" placeholder="请输入百度OCR的Secret Key"/>
          <span class="desc">百度OCR服务的Secret Key</span>
        </FormItem>
      </template>

      <!-- 腾讯云OCR配置 -->
      <template v-if="formValidate.ocrPlatform === 'TENCENT_OCR'">
        <Divider orientation="left">腾讯云OCR配置</Divider>
        <FormItem label="SecretId" prop="tencentOcrSecretId">
          <Input v-model="formValidate.tencentOcrSecretId" placeholder="请输入腾讯云OCR的SecretId"/>
          <span class="desc">腾讯云OCR服务的SecretId</span>
        </FormItem>
        <FormItem label="SecretKey" prop="tencentOcrSecretKey">
          <Input v-model="formValidate.tencentOcrSecretKey" type="password" placeholder="请输入腾讯云OCR的SecretKey"/>
          <span class="desc">腾讯云OCR服务的SecretKey</span>
        </FormItem>
        <FormItem label="Region" prop="tencentOcrRegion">
          <Select v-model="formValidate.tencentOcrRegion" placeholder="请选择区域">
            <Option value="ap-beijing">北京</Option>
            <Option value="ap-shanghai">上海</Option>
            <Option value="ap-guangzhou">广州</Option>
            <Option value="ap-chengdu">成都</Option>
          </Select>
          <span class="desc">选择腾讯云OCR服务的区域</span>
        </FormItem>
      </template>

      <!-- OCR功能配置 -->
      <Divider orientation="left">OCR功能配置</Divider>
      <FormItem label="身份证识别" prop="enableIdCardOcr">
        <RadioGroup v-model="formValidate.enableIdCardOcr" type="button">
          <Radio label="true">启用</Radio>
          <Radio label="false">禁用</Radio>
        </RadioGroup>
        <span class="desc">是否启用身份证OCR识别功能</span>
      </FormItem>
      
      <FormItem label="营业执照识别" prop="enableBusinessLicenseOcr">
        <RadioGroup v-model="formValidate.enableBusinessLicenseOcr" type="button">
          <Radio label="true">启用</Radio>
          <Radio label="false">禁用</Radio>
        </RadioGroup>
        <span class="desc">是否启用营业执照OCR识别功能</span>
      </FormItem>

      <FormItem label="银行卡识别" prop="enableBankCardOcr">
        <RadioGroup v-model="formValidate.enableBankCardOcr" type="button">
          <Radio label="true">启用</Radio>
          <Radio label="false">禁用</Radio>
        </RadioGroup>
        <span class="desc">是否启用银行卡OCR识别功能</span>
      </FormItem>

      <FormItem label="通用文字识别" prop="enableGeneralOcr">
        <RadioGroup v-model="formValidate.enableGeneralOcr" type="button">
          <Radio label="true">启用</Radio>
          <Radio label="false">禁用</Radio>
        </RadioGroup>
        <span class="desc">是否启用通用文字OCR识别功能</span>
      </FormItem>

      <!-- 测试功能 -->
      <Divider orientation="left">测试配置</Divider>
      <FormItem label="测试模式" prop="ocrTestMode">
        <RadioGroup v-model="formValidate.ocrTestMode" type="button">
          <Radio label="true">开启</Radio>
          <Radio label="false">关闭</Radio>
        </RadioGroup>
        <span class="desc">测试模式下将返回模拟数据，不消耗实际API调用次数</span>
      </FormItem>

      <div class="label-btns">
        <Button type="primary" @click="submit('formValidate')">保存</Button>
        <Button type="default" @click="testConnection" style="margin-left: 10px;">测试连接</Button>
      </div>
    </Form>
  </div>
</template>

<script>
import {setSetting} from "@/api/index";
import {handleSubmit} from "./validate";

export default {
  data() {
    return {
      result: "",
      ruleValidate: {}, // 验证规则
      formValidate: { // 表单数据
        // OCR平台选择
        ocrPlatform: "ALI_OCR",
        
        // 阿里云OCR配置
        aliOcrAccessKeyId: "",
        aliOcrAccessSecret: "",
        aliOcrRegion: "cn-hangzhou",
        
        // 百度OCR配置
        baiduOcrApiKey: "",
        baiduOcrSecretKey: "",
        
        // 腾讯云OCR配置
        tencentOcrSecretId: "",
        tencentOcrSecretKey: "",
        tencentOcrRegion: "ap-beijing",
        
        // OCR功能开关
        enableIdCardOcr: "true",
        enableBusinessLicenseOcr: "true",
        enableBankCardOcr: "true",
        enableGeneralOcr: "true",
        
        // 测试模式
        ocrTestMode: "false",
      },
    };
  },
  props: ["res", "type"],
  created() {
    this.init();
  },
  methods: {
    // 保存
    submit(name) {
      let that = this;
      if (handleSubmit(that, name)) {
        this.setupSetting()
      }
    },
    // 保存设置
    setupSetting() {
      setSetting(this.type, this.formValidate).then((res) => {
        if (res.success) {
          this.$Message.success("保存成功!");
        } else {
          this.$Message.error("保存失败!");
        }
      });
    },
    // 测试连接
    testConnection() {
      this.$Message.info("正在测试OCR服务连接...");
      // 这里可以添加测试OCR服务连接的逻辑
      setTimeout(() => {
        this.$Message.success("OCR服务连接测试成功!");
      }, 2000);
    },
    // 实例化数据
    init() {
      this.result = JSON.parse(this.res);

      Object.keys(this.result).map((item) => {
        this.result[item] += "";
      });

      this.$set(this, "formValidate", {...this.result});
      Object.keys(this.formValidate).forEach((item) => {
        this.ruleValidate[item] = [
          {
            required: true,
            message: "请填写必填项",
            trigger: "blur",
          },
        ];
      });
    },
  },
};
</script>

<style lang="scss" scoped>
@import "./style.scss";

.label-item {
  display: flex;
}

/deep/ .ivu-input {
  width: 300px !important;
  margin: 0 10px;
}

.ivu-input-wrapper {
  width: 300px;
  margin-right: 10px;
}

/deep/ .ivu-select {
  width: 300px !important;
}

.desc {
  color: #999;
  font-size: 12px;
  margin-left: 10px;
}
</style>
